# 福彩3D数据库维护指南

## 📋 概述

本指南详细说明福彩3D预测系统的数据库架构、维护流程和故障排除方法。

## 🗄️ 数据库架构

### 主要数据库文件

1. **data/lottery.db** - 主数据库
   - 存储历史开奖数据
   - 包含lottery_data表（8364条真实记录）
   - 用于LotteryQueryEngine查询开奖结果

2. **data/fucai3d.db** - 融合预测数据库
   - 存储预测结果和融合数据
   - 包含fusion_predictions表
   - 用于ClosedLoopSystem获取预测记录

### 核心数据表

#### lottery_data表结构
```sql
CREATE TABLE lottery_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL UNIQUE,
    draw_date TEXT NOT NULL,
    hundreds INTEGER NOT NULL,
    tens INTEGER NOT NULL,
    units INTEGER NOT NULL,
    sum_value INTEGER,
    span_value INTEGER,
    ac_value INTEGER,
    hundreds_form TEXT,
    tens_form TEXT,
    units_form TEXT,
    form_type TEXT,
    trial_number TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_source TEXT DEFAULT 'official'
);
```

#### fusion_predictions表结构
```sql
CREATE TABLE fusion_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    session_id TEXT,
    combination TEXT,
    rank INTEGER,
    timestamp TEXT,
    confidence_score REAL,
    model_version TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 维护操作

### 数据库状态检查

```bash
# 检查数据库文件和表结构
python check_db_status.py

# 检查表结构和数据样本
python check_table_structure.py

# 测试数据库连接
python test_database_connections.py
```

### 数据导入和更新

```bash
# 更新历史开奖数据
python src/data/updater.py

# 创建测试预测数据
python create_testable_predictions.py
```

### 复盘功能测试

```bash
# 测试复盘功能
curl -X POST http://127.0.0.1:8000/api/review/manual

# 查看复盘历史
curl http://127.0.0.1:8000/api/review/history?limit=20&days=30
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. "no such table: lottery_data"
**原因**: 数据库文件不存在或表未创建
**解决方案**:
```bash
python src/database/init_db.py
python src/data/updater.py
```

#### 2. "no such table: fusion_predictions"
**原因**: 融合预测表未创建
**解决方案**:
```bash
python create_testable_predictions.py
```

#### 3. "no such column: combination"
**原因**: ReviewEngine连接错误的数据库
**解决方案**: 确保ReviewEngine使用正确的数据库路径配置

#### 4. 复盘返回False
**原因**: 数据库连接配置错误或数据缺失
**解决方案**:
1. 检查数据库文件存在性
2. 验证数据完整性
3. 测试数据库连接

### 数据库连接配置

#### ClosedLoopSystem配置
```python
# src/automation/closed_loop_system.py
self.db_path = "data/lottery.db"          # 主数据库
self.fusion_db_path = "data/fucai3d.db"   # 融合数据库

# ReviewEngine使用主数据库查询开奖数据
self.review_engine = ReviewEngine(self.db_path)
```

#### ReviewEngine配置
```python
# src/analysis/review_engine.py
self.db_path = db_path                    # 开奖数据库
self.fusion_db_path = "data/fucai3d.db"   # 预测数据库

# 查询开奖数据使用db_path
# 查询预测数据使用fusion_db_path
```

## 📊 数据质量监控

### 数据完整性检查

```sql
-- 检查历史数据完整性
SELECT COUNT(*) FROM lottery_data;
SELECT MIN(issue), MAX(issue) FROM lottery_data;
SELECT COUNT(*) FROM lottery_data WHERE hundreds IS NULL OR tens IS NULL OR units IS NULL;

-- 检查预测数据
SELECT COUNT(*) FROM fusion_predictions;
SELECT DISTINCT issue FROM fusion_predictions ORDER BY issue DESC LIMIT 10;
```

### 性能监控

```bash
# 数据库文件大小
ls -lh data/*.db

# 查询性能测试
time python test_database_connections.py
```

## 🔄 备份和恢复

### 数据备份

```bash
# 创建数据库备份
cp data/lottery.db data/backup/lottery_$(date +%Y%m%d).db
cp data/fucai3d.db data/backup/fucai3d_$(date +%Y%m%d).db
```

### 数据恢复

```bash
# 从备份恢复
cp data/backup/lottery_20250810.db data/lottery.db
cp data/backup/fucai3d_20250810.db data/fucai3d.db
```

## 📈 性能优化

### 索引优化

```sql
-- 为常用查询字段创建索引
CREATE INDEX IF NOT EXISTS idx_lottery_issue ON lottery_data(issue);
CREATE INDEX IF NOT EXISTS idx_lottery_date ON lottery_data(draw_date);
CREATE INDEX IF NOT EXISTS idx_fusion_issue ON fusion_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_fusion_timestamp ON fusion_predictions(timestamp);
```

### 查询优化

- 使用参数化查询避免SQL注入
- 限制查询结果数量
- 使用连接池管理数据库连接

## 🔍 监控和告警

### 关键指标

1. **数据完整性**: lottery_data表记录数应为8364条
2. **数据时效性**: 最新记录日期应接近当前日期
3. **复盘成功率**: 手动复盘应返回True
4. **查询性能**: 数据库查询响应时间 < 1秒

### 告警条件

- 数据库文件不存在
- 关键表缺失或为空
- 复盘功能连续失败
- 查询响应时间过长

## 📝 维护日志

### 2025-08-10 修复记录

**问题**: 手动复盘失败，返回False
**原因**: 
1. ReviewEngine数据库路径配置错误
2. 预测数据表列名不匹配
3. 数据库连接配置混乱

**解决方案**:
1. 修复ClosedLoopSystem中ReviewEngine的数据库路径
2. 修复ReviewEngine中预测数据查询的数据库连接
3. 创建基于真实历史数据的测试预测记录

**结果**: 复盘功能完全正常，准确率计算正确

## 🎯 最佳实践

1. **数据真实性**: 严禁使用虚拟数据，所有测试必须基于真实历史记录
2. **定期备份**: 每日备份关键数据库文件
3. **监控告警**: 建立自动化监控和告警机制
4. **文档更新**: 及时更新维护文档和操作记录
5. **测试验证**: 重要修改后必须进行完整功能测试

## 📞 技术支持

如遇到数据库相关问题，请：
1. 查看本维护指南
2. 运行诊断脚本
3. 检查系统日志
4. 记录问题详情和解决过程
