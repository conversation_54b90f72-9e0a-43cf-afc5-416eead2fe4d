#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D数据同步脚本
从真实数据源自动获取最新开奖数据并更新数据库

数据源: https://data.17500.cn/3d_asc.txt
更新频率: 每日开奖后
"""

import requests
import sqlite3
import datetime
import logging
import sys
import os
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/sync_lottery_data.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class LotteryDataSyncer:
    """福彩3D数据同步器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        self.db_path = db_path
        self.data_source_url = "https://data.17500.cn/3d_asc.txt"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def fetch_latest_data(self) -> Optional[List[Dict]]:
        """从数据源获取最新开奖数据"""
        try:
            logger.info(f"正在从 {self.data_source_url} 获取数据...")
            response = requests.get(self.data_source_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # 解析数据
            lines = response.text.strip().split('\n')
            lottery_data = []
            
            for line in lines[-10:]:  # 获取最新10期数据
                if line.strip():
                    parts = line.strip().split()
                    if len(parts) >= 3:
                        issue = parts[0]
                        date_str = parts[1]
                        numbers = parts[2]
                        
                        if len(numbers) == 3 and numbers.isdigit():
                            lottery_data.append({
                                'issue': issue,
                                'date': date_str,
                                'numbers': numbers,
                                'hundreds': int(numbers[0]),
                                'tens': int(numbers[1]),
                                'units': int(numbers[2])
                            })
            
            logger.info(f"成功获取 {len(lottery_data)} 期数据")
            return lottery_data
            
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None
    
    def get_db_latest_issue(self) -> Optional[str]:
        """获取数据库中最新的期号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"查询数据库失败: {e}")
            return None
    
    def update_database(self, lottery_data: List[Dict]) -> int:
        """更新数据库中的开奖数据"""
        updated_count = 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for data in lottery_data:
                # 检查期号是否存在
                cursor.execute("SELECT id FROM final_predictions WHERE issue = ?", (data['issue'],))
                exists = cursor.fetchone()
                
                if exists:
                    # 更新现有数据
                    cursor.execute("""
                        UPDATE final_predictions 
                        SET hundreds = ?, tens = ?, units = ?
                        WHERE issue = ?
                    """, (data['hundreds'], data['tens'], data['units'], data['issue']))
                    
                    if cursor.rowcount > 0:
                        logger.info(f"更新期号 {data['issue']}: {data['numbers']}")
                        updated_count += 1
                else:
                    logger.info(f"期号 {data['issue']} 不存在于数据库中，跳过")
            
            conn.commit()
            conn.close()
            
            logger.info(f"数据库更新完成，共更新 {updated_count} 期数据")
            return updated_count
            
        except Exception as e:
            logger.error(f"更新数据库失败: {e}")
            return 0
    
    def validate_data(self, lottery_data: List[Dict]) -> bool:
        """验证数据的有效性"""
        if not lottery_data:
            logger.warning("数据为空")
            return False
        
        for data in lottery_data:
            # 验证期号格式
            if not data['issue'].isdigit() or len(data['issue']) != 7:
                logger.warning(f"期号格式错误: {data['issue']}")
                return False
            
            # 验证号码范围
            if not (0 <= data['hundreds'] <= 9 and 
                   0 <= data['tens'] <= 9 and 
                   0 <= data['units'] <= 9):
                logger.warning(f"号码范围错误: {data['numbers']}")
                return False
        
        return True
    
    def sync(self, force_update: bool = False) -> bool:
        """执行数据同步"""
        logger.info("开始数据同步...")
        
        # 获取最新数据
        latest_data = self.fetch_latest_data()
        if not latest_data:
            logger.error("获取数据失败")
            return False
        
        # 验证数据
        if not self.validate_data(latest_data):
            logger.error("数据验证失败")
            return False
        
        # 检查是否需要更新
        if not force_update:
            db_latest = self.get_db_latest_issue()
            source_latest = latest_data[-1]['issue'] if latest_data else None
            
            if db_latest and source_latest and db_latest >= source_latest:
                logger.info(f"数据库已是最新 (期号: {db_latest})")
                return True
        
        # 更新数据库
        updated_count = self.update_database(latest_data)
        
        if updated_count > 0:
            logger.info(f"同步成功，更新了 {updated_count} 期数据")
            return True
        else:
            logger.warning("没有数据被更新")
            return False

def main():
    """主函数"""
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 创建同步器
    syncer = LotteryDataSyncer()
    
    # 执行同步
    force_update = '--force' in sys.argv
    success = syncer.sync(force_update=force_update)
    
    if success:
        logger.info("数据同步完成")
        sys.exit(0)
    else:
        logger.error("数据同步失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
